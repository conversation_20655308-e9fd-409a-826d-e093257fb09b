// 烤串合成游戏
function useKaoGame(canvasRef, onGameEnd) {
    // 食物列表
    const foodList = [
        {
            id: 0,
            name: '香菇',
            src: 'img/food1.png',
            synthetic: 'img/food1_synthetic.png',
            width: 160,
            syntheticWidth: 160,
            img: null,
            syntheticImg: null,
            aspectRatio: 1,
            syntheticAspectRatio: 1
        },
        {
            id: 1,
            name: '鸡翅',
            src: 'img/food2.png',
            synthetic: 'img/food2_synthetic.png',
            width: 160,
            syntheticWidth: 160,
            img: null,
            syntheticImg: null,
            aspectRatio: 1,
            syntheticAspectRatio: 1
        },
        {
            id: 2,
            name: '海带',
            src: 'img/food3.png',
            synthetic: 'img/food3_synthetic.png',
            width: 160,
            syntheticWidth: 160,
            img: null,
            syntheticImg: null,
            aspectRatio: 1,
            syntheticAspectRatio: 1
        },
        {
            id: 3,
            name: '龙虾',
            src: 'img/food4.png',
            synthetic: 'img/food4_synthetic.png',
            width: 160,
            syntheticWidth: 160,
            img: null,
            syntheticImg: null,
            aspectRatio: 1,
            syntheticAspectRatio: 1
        },
        {
            id: 4,
            name: '鱿鱼',
            src: 'img/food5.png',
            synthetic: 'img/food5_synthetic.png',
            width: 160,
            syntheticWidth: 160,
            img: null,
            syntheticImg: null,
            aspectRatio: 1,
            syntheticAspectRatio: 1
        },
        {
            id: 5,
            name: '烤肠',
            src: 'img/food6.png',
            synthetic: 'img/food6_synthetic.png',
            width: 160,
            syntheticWidth: 160,
            img: null,
            syntheticImg: null,
            aspectRatio: 1,
            syntheticAspectRatio: 1
        },
        {
            id: 6,
            name: '玉米',
            src: 'img/food7.png',
            synthetic: 'img/food7_synthetic.png',
            width: 160,
            syntheticWidth: 160,
            img: null,
            syntheticImg: null,
            aspectRatio: 1,
            syntheticAspectRatio: 1
        },
        {
            id: 7,
            name: '烤肉',
            src: 'img/food8.png',
            synthetic: 'img/food8_synthetic.png',
            width: 160,
            syntheticWidth: 160,
            img: null,
            syntheticImg: null,
            aspectRatio: 1,
            syntheticAspectRatio: 1
        }
    ];

    // 游戏配置
    const config = {
        gameTime: 60,           // 游戏时间
        canvasWidth: 750,       // 画布宽度
        canvasHeight: 1334,     // 画布高度
        topSpaceRatio: 0.1,     // 上部空间占比20%
        stickWidth: 16,         // 木签宽度
        stickHeight: 485,       // 木签高度
        maxFoodsPerStick: 4,    // 每根木签最多食物数量
        animationDuration: 300, // 动画持续时间
        selectOffset: 15,       // 选中时向上偏移距离
        selectAnimationDuration: 200, // 选中动画持续时间(毫秒)
        moveAnimationDuration: 400,   // 移动动画持续时间(毫秒)
    };

    // 动态计算木签位置
    const calculateStickPositions = (topStickCount = 2, bottomStickCount = 1) => {
        const gameAreaHeight = config.canvasHeight * (1 - config.topSpaceRatio); // 游戏可用区域高度
        const gameAreaTop = config.canvasHeight * config.topSpaceRatio; // 游戏区域起始Y坐标

        // 将游戏区域分为上下两部分
        const topSectionHeight = gameAreaHeight / 2;
        const bottomSectionHeight = gameAreaHeight / 2;

        // 计算各部分的中心Y坐标
        const topSectionCenterY = gameAreaTop + topSectionHeight / 2;
        const bottomSectionCenterY = gameAreaTop + topSectionHeight + bottomSectionHeight / 2;

        // 动态计算上部分木签位置
        const topSticks = [];
        if (topStickCount > 0) {
            for (let i = 0; i < topStickCount; i++) {
                const xRatio = (i + 1) / (topStickCount + 1); // 均匀分布
                topSticks.push({
                    x: config.canvasWidth * xRatio,
                    y: topSectionCenterY
                });
            }
        }

        // 动态计算下部分木签位置
        const bottomSticks = [];
        if (bottomStickCount > 0) {
            for (let i = 0; i < bottomStickCount; i++) {
                const xRatio = (i + 1) / (bottomStickCount + 1); // 均匀分布
                bottomSticks.push({
                    x: config.canvasWidth * xRatio,
                    y: bottomSectionCenterY
                });
            }
        }

        return {
            top: topSticks,
            bottom: bottomSticks
        };
    };

    // 关卡配置
    const levelConfigs = {
        1: {
            topStickCount: 2,
            bottomStickCount: 1,
            foodTypes: [0, 1], // 香菇和鸡翅
            foodsPerStick: 4,
            targetSyntheticFoods: 2 // 需要合成2个食物
        },
        2: {
            topStickCount: 3,
            bottomStickCount: 2,
            foodTypes: [2, 3, 4], // 香菇、鸡翅、海带
            foodsPerStick: 4,
            targetSyntheticFoods: 3
        },
        3: {
            topStickCount: 4,
            bottomStickCount: 2,
            foodTypes: [4, 5, 6, 7], // 前4种食物
            foodsPerStick: 4,
            targetSyntheticFoods: 4
        }
    };

    // 游戏状态
    const gameState = ref({
        timeLeft: config.gameTime,
        isRunning: false,
        isGameOver: false,
        currentLevel: 1,         // 当前关卡
        selectedFoods: null,     // 选中的食物信息 {stickIndex, foodIndex, count}
        selectAnimation: null,   // 选中动画状态 {startTime, isSelecting}
        moveAnimations: [],      // 移动动画列表
        sticks: [],              // 木签状态
        animations: [],          // 动画列表
        successAnimations: []    // 成功动画列表
    });

    // 音效
    const successSound = new Howl({
        src: ['./pop1.mp3'],
        volume: 0.5,
        html5: true,
        preload: true
    });

    // 图片资源
    const images = {
        stick: null,
        light: null
    };

    let ctx = null;
    let animationFrame = null;
    let timerInterval = null;

    const easeInOutCubic = (t) => {
        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    };

    const easeOutElastic = (t) => {
        const c4 = (2 * Math.PI) / 3;
        return t === 0 ? 0 : t === 1 ? 1 : Math.pow(2, -10 * t) * Math.sin((t * 10 - 0.75) * c4) + 1;
    };

    // 添加移动动画
    const addMoveAnimation = (fromStickIndex, toStickIndex, count, onComplete) => {
        const fromStick = gameState.value.sticks[fromStickIndex];
        const toStick = gameState.value.sticks[toStickIndex];

        // 临时保存要移动的食物数据
        const movingFoods = fromStick.foods.slice(0, count);

        // 创建移动动画对象
        const moveAnimation = {
            startTime: Date.now(),
            duration: config.moveAnimationDuration,
            fromX: fromStick.x,
            fromY: fromStick.y - config.stickHeight / 4, // 从木签上方开始
            toX: toStick.x,
            toY: toStick.y - config.stickHeight / 4,     // 到木签上方结束
            fromStickIndex: fromStickIndex,
            toStickIndex: toStickIndex,
            count: count,
            movingFoods: movingFoods, // 保存移动的食物数据
            onComplete: () => {
                // 动画完成后，将食物添加到目标木签
                toStick.foods.unshift(...movingFoods);

                // 检查目标木签是否可以合成
                if (checkSynthesis(toStickIndex)) {
                    setTimeout(() => {
                        performSynthesis(toStickIndex);
                    }, 100);
                }

                // 调用外部回调
                if (onComplete) {
                    onComplete();
                }
            }
        };

        // 临时从源木签移除食物（视觉上）
        fromStick.foods.splice(0, count);

        gameState.value.moveAnimations.push(moveAnimation);
    };

    // 绘制移动动画
    const drawMoveAnimations = () => {
        gameState.value.moveAnimations.forEach((anim, index) => {
            const elapsed = Date.now() - anim.startTime;
            const progress = Math.min(elapsed / anim.duration, 1);

            if (progress >= 1) {
                // 动画完成，执行回调
                if (anim.onComplete) {
                    anim.onComplete();
                }
                gameState.value.moveAnimations.splice(index, 1);
                return;
            }

            // 使用贝塞尔曲线创建抛物线轨迹
            const easedProgress = easeInOutCubic(progress);
            const currentX = anim.fromX + (anim.toX - anim.fromX) * easedProgress;
            const currentY = anim.fromY + (anim.toY - anim.fromY) * easedProgress;

            // 添加抛物线效果
            const arcHeight = 80;
            const arcY = currentY - Math.sin(progress * Math.PI) * arcHeight;

            // 绘制移动中的食物（绘制所有移动的食物）
            anim.movingFoods.forEach((food, foodIndex) => {
                const foodData = foodList[food.type];
                if (foodData) {
                    const img = food.isSynthetic ? foodData.syntheticImg : foodData.img;
                    if (img && img.complete) {
                        const foodWidth = food.isSynthetic ? foodData.syntheticWidth : foodData.width;
                        const aspectRatio = food.isSynthetic ? foodData.syntheticAspectRatio : foodData.aspectRatio;
                        const foodHeight = foodWidth * aspectRatio;

                        // 为每个食物添加轻微的偏移，避免重叠
                        const offsetX = (foodIndex - (anim.count - 1) / 2) * 20;
                        const offsetY = foodIndex * 10;

                        // 添加旋转和缩放效果
                        ctx.save();
                        ctx.translate(currentX + offsetX, arcY + offsetY);
                        ctx.rotate(progress * Math.PI * 1.5 + foodIndex * 0.5); // 每个食物旋转角度略有不同
                        ctx.scale(0.7 + 0.3 * Math.sin(progress * Math.PI), 0.7 + 0.3 * Math.sin(progress * Math.PI)); // 缩放效果

                        // 添加阴影
                        ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
                        ctx.shadowBlur = 8;
                        ctx.shadowOffsetY = 3;

                        ctx.drawImage(img,
                            -foodWidth / 2,
                            -foodHeight / 2,
                            foodWidth,
                            foodHeight
                        );

                        ctx.restore();
                    }
                }
            });
        });
    };

    // 加载图片资源
    const loadAssets = () => {
        return new Promise((resolve) => {
            let totalImages = foodList.length * 2 + 2; // 食物图片 + 合成图片 + 木签 + 光效
            let loadedImages = 0;

            console.log('开始加载图片资源...');
            console.log(`需要加载的图片总数: ${totalImages}`);

            const onLoad = () => {
                loadedImages++;
                console.log(`已加载图片: ${loadedImages}/${totalImages}`);
                if (loadedImages === totalImages) {
                    console.log('所有图片加载完成');
                    // 计算所有图片的宽高比
                    foodList.forEach((food) => {
                        if (food.img) {
                            food.aspectRatio = food.img.height / food.img.width;
                        }
                        if (food.syntheticImg) {
                            food.syntheticAspectRatio = food.syntheticImg.height / food.syntheticImg.width;
                        }
                    });
                    resolve();
                }
            };

            // 加载食物图片
            foodList.forEach((food) => {
                // 加载普通食物图片
                const img = new Image();
                img.onload = onLoad;
                img.onerror = (e) => {
                    console.error('加载食物图片失败:', food.src, e);
                    onLoad(); // 即使失败也要继续
                };
                console.log(`开始加载食物图片: ${food.src}`);
                img.src = food.src;
                food.img = img;

                // 加载合成食物图片
                const syntheticImg = new Image();
                syntheticImg.onload = onLoad;
                syntheticImg.onerror = (e) => {
                    console.error('加载合成食物图片失败:', food.synthetic, e);
                    onLoad(); // 即使失败也要继续
                };
                console.log(`开始加载合成食物图片: ${food.synthetic}`);
                syntheticImg.src = food.synthetic;
                food.syntheticImg = syntheticImg;
            });

            // 加载木签图片
            images.stick = new Image();
            images.stick.onload = onLoad;
            images.stick.onerror = (e) => {
                console.error('加载木签图片失败:', 'img/qian.png', e);
                onLoad();
            };
            console.log('开始加载木签图片: img/qian.png');
            images.stick.src = 'img/qian.png';

            // 加载光效图片
            images.light = new Image();
            images.light.onload = onLoad;
            images.light.onerror = (e) => {
                console.error('加载光效图片失败:', 'img/light.png', e);
                onLoad();
            };
            console.log('开始加载光效图片: img/light.png');
            images.light.src = 'img/light.png';
        });
    };

    // 随机分配食物到木签上
    const generateRandomFoodDistribution = (foodTypes, totalSticks, foodsPerStick) => {
        // 创建食物池：每种食物4个，确保能够合成
        const foodPool = [];
        foodTypes.forEach(foodType => {
            for (let i = 0; i < 4; i++) {
                foodPool.push({
                    type: foodType,
                    isSynthetic: false
                });
            }
        });

        // 打乱食物池
        for (let i = foodPool.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [foodPool[i], foodPool[j]] = [foodPool[j], foodPool[i]];
        }

        // 分配食物到木签
        const stickFoods = Array(totalSticks).fill(null).map(() => []);
        let currentStick = 0;

        for (const food of foodPool) {
            // 寻找一个合适的木签放置食物
            let placed = false;
            let attempts = 0;
            const maxAttempts = totalSticks * 2;

            while (!placed && attempts < maxAttempts) {
                const stickIndex = currentStick % totalSticks;
                const stick = stickFoods[stickIndex];

                // 检查这根木签是否可以放置这个食物
                if (stick.length < foodsPerStick) {
                    // 计算当前木签上这种食物的数量
                    const sameTypeCount = stick.filter(f => f.type === food.type).length;

                    // 如果放置后不会超过3个同类食物，则可以放置
                    if (sameTypeCount < 3) {
                        stick.push(food);
                        placed = true;
                    }
                }

                currentStick++;
                attempts++;
            }

            // 如果无法放置，强制放到第一个有空间的木签（作为备选方案）
            if (!placed) {
                for (let i = 0; i < totalSticks; i++) {
                    if (stickFoods[i].length < foodsPerStick) {
                        stickFoods[i].push(food);
                        break;
                    }
                }
            }
        }

        return stickFoods;
    };

    // 初始化木签状态
    const initSticks = (level = null) => {
        const currentLevel = level || gameState.value.currentLevel;
        const currentConfig = levelConfigs[currentLevel] || levelConfigs[1]; // 默认使用第一关配置
        const sticks = [];
        const stickPositions = calculateStickPositions(currentConfig.topStickCount, currentConfig.bottomStickCount);

        // 随机分配食物
        const randomFoodDistribution = generateRandomFoodDistribution(
            currentConfig.foodTypes,
            currentConfig.topStickCount,
            currentConfig.foodsPerStick
        );

        // 上部分木签，使用随机分配的食物
        stickPositions.top.forEach((pos, index) => {
            const foods = randomFoodDistribution[index] || [];
            sticks.push({
                x: pos.x,
                y: pos.y,
                foods: foods,
                isSelected: false
            });
        });

        // 下部分空木签
        stickPositions.bottom.forEach((pos) => {
            sticks.push({
                x: pos.x,
                y: pos.y,
                foods: [],
                isSelected: false
            });
        });

        gameState.value.sticks = sticks;
    };

    // 获取木签上最上面的连续相同食物（从数组开头开始，视觉上靠上）
    const getTopSameFoods = (stickIndex) => {
        const stick = gameState.value.sticks[stickIndex];
        if (stick.foods.length === 0) return { count: 0, type: null };

        const topFood = stick.foods[0]; // 数组第一个元素是视觉上最上面的食物
        let count = 1;

        // 从最上面开始向下查找相同类型的连续食物
        for (let i = 1; i < stick.foods.length; i++) {
            if (stick.foods[i].type === topFood.type &&
                stick.foods[i].isSynthetic === topFood.isSynthetic) {
                count++;
            } else {
                break;
            }
        }

        return { count, type: topFood.type, isSynthetic: topFood.isSynthetic };
    };

    // 检查是否可以移动到目标木签
    const canMoveTo = (targetStickIndex, foodCount) => {
        const targetStick = gameState.value.sticks[targetStickIndex];
        const remainingSpace = config.maxFoodsPerStick - targetStick.foods.length;

        // 如果是合成食物，只能放1个，且木签必须为空
        if (foodCount === 1 && gameState.value.selectedFoods?.isSynthetic) {
            return targetStick.foods.length === 0;
        }

        return remainingSpace >= foodCount;
    };

    // 检查是否可以合成
    const checkSynthesis = (stickIndex) => {
        const stick = gameState.value.sticks[stickIndex];
        if (stick.foods.length !== 4) return false;

        // 检查是否都是相同类型的普通食物
        const firstFood = stick.foods[0];
        if (firstFood.isSynthetic) return false;

        return stick.foods.every(food =>
            food.type === firstFood.type && !food.isSynthetic
        );
    };

    // 执行合成
    const performSynthesis = (stickIndex) => {
        const stick = gameState.value.sticks[stickIndex];

        // 播放成功音效
        successSound.play();

        // 添加成功动画
        gameState.value.successAnimations.push({
            x: stick.x,
            y: stick.y - 100,
            startTime: Date.now(),
            scale: 1
        });

        // 替换为合成食物
        const foodType = stick.foods[0].type;
        stick.foods = [{
            type: foodType,
            isSynthetic: true
        }];

        // 延迟检查关卡进阶（等待动画播放一段时间）
        setTimeout(() => {
            checkLevelProgression();
        }, 500);
    };

    // 检查关卡进阶
    const checkLevelProgression = () => {
        if (!gameState.value.isRunning) return;

        // 检查当前关卡是否完成
        if (checkWin()) {
            const currentLevel = gameState.value.currentLevel;
            // 立即暂停倒计时
            clearInterval(timerInterval);
            if (currentLevel < 3) {
                // 还有下一关，进入下一关
                const nextLevel = currentLevel + 1;
                console.log(`第${currentLevel}关完成！进入第${nextLevel}关`);

                // 显示关卡完成提示
                showLevelCompleteAnimation(currentLevel, () => {
                    // 动画完成后进入下一关
                    advanceToNextLevel(nextLevel);
                });
            } else {
                // 第三关完成，游戏胜利
                console.log('恭喜！所有关卡完成！');
                setTimeout(() => {
                    endGame();
                }, 1000); // 延迟一秒让玩家看到最后的合成效果
            }
        }
    };

    // 显示关卡完成动画
    const showLevelCompleteAnimation = (completedLevel, onComplete) => {
        // 添加关卡完成动画
        gameState.value.successAnimations.push({
            x: config.canvasWidth / 2,
            y: config.canvasHeight / 2,
            startTime: Date.now(),
            scale: 1,
            text: `第${completedLevel}关完成！`,
            isLevelComplete: true
        });

        // 2秒后执行回调
        setTimeout(() => {
            if (onComplete) {
                onComplete();
            }
        }, 2000);
    };

    // 进入下一关
    const advanceToNextLevel = (nextLevel) => {
        // 更新关卡
        gameState.value.currentLevel = nextLevel;

        // 重置时间
        gameState.value.timeLeft = config.gameTime;
        // 重新开始倒计时
        startTimer();

        // 清除选中状态和动画
        gameState.value.selectedFoods = null;
        gameState.value.selectAnimation = null;
        gameState.value.moveAnimations = [];
        gameState.value.successAnimations = [];

        // 初始化新关卡的木签
        initSticks(nextLevel);

        console.log(`开始第${nextLevel}关`);
    };

    // 绘制木签
    const drawStick = (stick, stickIndex) => {
        // 绘制木签
        if (images.stick && images.stick.complete) {
            ctx.drawImage(images.stick,
                stick.x - config.stickWidth / 2,
                stick.y - config.stickHeight / 2,
                config.stickWidth,
                config.stickHeight
            );
        }

        // 绘制食物
        if (stick.foods.length > 0) {
            const foodSpacing = 5; // 食物之间的间距
            const maxStickContentHeight = config.stickHeight * 0.8; // 木签内容区域最大高度（留20%边距）

            // 预计算所有食物的尺寸信息
            const foodInfos = stick.foods.map(food => {
                const foodData = foodList[food.type];
                const img = food.isSynthetic ? foodData.syntheticImg : foodData.img;
                const aspectRatio = food.isSynthetic ? foodData.syntheticAspectRatio : foodData.aspectRatio;
                let foodWidth = food.isSynthetic ? foodData.syntheticWidth : foodData.width;
                let foodHeight = foodWidth * aspectRatio;

                return {
                    food,
                    foodData,
                    img,
                    aspectRatio,
                    width: foodWidth,
                    height: foodHeight
                };
            });

            // 计算所有食物的总高度（使用实际高度）
            let totalFoodHeight = foodInfos.reduce((total, info) => total + info.height, 0) +
                                 (stick.foods.length - 1) * foodSpacing;

            // 如果总高度超过木签容量，按比例缩小所有食物
            let scaleFactor = 1;
            if (totalFoodHeight > maxStickContentHeight) {
                scaleFactor = maxStickContentHeight / totalFoodHeight;

                // 重新计算缩放后的尺寸
                foodInfos.forEach(info => {
                    info.width *= scaleFactor;
                    info.height *= scaleFactor;
                });

                totalFoodHeight = maxStickContentHeight;
            }

            // 计算起始Y坐标，使食物在木签上居中
            const startY = stick.y - totalFoodHeight / 2;

            // 绘制每个食物
            let currentY = startY;
            const scaledSpacing = foodSpacing * scaleFactor; // 缩放后的间距

            foodInfos.forEach((info, foodIndex) => {
                if (info.img && info.img.complete) {
                    let foodY = currentY;

                    // 如果是选中的食物，添加动画效果
                    const selectedFoods = gameState.value.selectedFoods;
                    if (selectedFoods && selectedFoods.stickIndex === stickIndex) {
                        // 选中的是从数组开头开始的食物
                        if (foodIndex < selectedFoods.count) {
                            // 计算选中动画进度
                            let animationProgress = 1;
                            let pulseScale = 1;
                            let glowAlpha = 0.8;

                            if (gameState.value.selectAnimation) {
                                const elapsed = Date.now() - gameState.value.selectAnimation.startTime;
                                const progress = Math.min(elapsed / config.selectAnimationDuration, 1);

                                if (gameState.value.selectAnimation.isSelecting) {
                                    // 选中动画：弹性效果
                                    animationProgress = easeOutElastic(progress);
                                } else {
                                    // 取消选中动画：反向
                                    animationProgress = 1 - easeInOutCubic(progress);
                                }

                                // 脉冲效果
                                pulseScale = 1 + Math.sin(elapsed * 0.008) * 0.05;
                                glowAlpha = 0.6 + Math.sin(elapsed * 0.01) * 0.2;
                            }

                            const offsetY = config.selectOffset * animationProgress;
                            foodY -= offsetY;

                            // 绘制发光效果
                            ctx.save();
                            ctx.globalAlpha = glowAlpha;
                            ctx.shadowColor = '#FFD700';
                            ctx.shadowBlur = 15 * scaleFactor;
                            ctx.shadowOffsetX = 0;
                            ctx.shadowOffsetY = 0;

                            // 绘制光效背景
                            if (images.light && images.light.complete) {
                                const lightPadding = 12 * scaleFactor * pulseScale;
                                ctx.drawImage(images.light,
                                    stick.x - info.width / 2 - lightPadding,
                                    foodY - lightPadding,
                                    info.width + lightPadding * 2,
                                    info.height + lightPadding * 2
                                );
                            }
                            ctx.restore();

                            // 应用缩放效果
                            ctx.save();
                            ctx.translate(stick.x, foodY + info.height / 2);
                            ctx.scale(pulseScale, pulseScale);
                            ctx.translate(-stick.x, -(foodY + info.height / 2));
                        }
                    }

                    // 绘制食物
                    ctx.drawImage(info.img,
                        stick.x - info.width / 2,
                        foodY,
                        info.width,
                        info.height
                    );

                    // 如果应用了选中效果，恢复canvas状态
                    if (selectedFoods && selectedFoods.stickIndex === stickIndex && foodIndex < selectedFoods.count) {
                        ctx.restore();
                    }

                    // 更新下一个食物的Y坐标
                    currentY += info.height + scaledSpacing;
                }
            });
        }
    };

    // 绘制成功动画
    const drawSuccessAnimations = () => {
        gameState.value.successAnimations.forEach((anim, index) => {
            const animDuration = anim.isLevelComplete ? 2000 : 1000; // 关卡完成动画持续2秒
            const progress = (Date.now() - anim.startTime) / animDuration;

            if (progress >= 1) {
                gameState.value.successAnimations.splice(index, 1);
                return;
            }

            ctx.save();

            if (anim.isLevelComplete) {
                // 关卡完成动画
                const scale = 1 + easeOutElastic(Math.min(progress * 2, 1)) * 0.3;
                const alpha = progress < 0.8 ? 1 : (1 - (progress - 0.8) / 0.2); // 最后0.2秒淡出

                ctx.globalAlpha = alpha;
                ctx.translate(anim.x, anim.y);
                ctx.scale(scale, scale);

                // 绘制背景
                ctx.fillStyle = 'rgba(255, 215, 0, 0.3)';
                ctx.fillRect(-200, -50, 400, 100);

                // 绘制关卡完成文字
                ctx.fillStyle = '#FFD700';
                ctx.strokeStyle = '#FF6B35';
                ctx.lineWidth = 3;
                ctx.font = 'bold 48px Arial';
                ctx.textAlign = 'center';
                ctx.strokeText(anim.text, 0, 0);
                ctx.fillText(anim.text, 0, 0);

                // 绘制副标题
                ctx.font = 'bold 24px Arial';
                ctx.fillStyle = '#FFF';
                ctx.fillText('准备进入下一关...', 0, 40);
            } else {
                // 普通合成成功动画
                const scale = 1 + Math.sin(progress * Math.PI) * 0.5;
                const alpha = 1 - progress;

                ctx.globalAlpha = alpha;
                ctx.translate(anim.x, anim.y);
                ctx.scale(scale, scale);

                // 绘制成功文字
                ctx.fillStyle = '#FFD700';
                ctx.font = 'bold 40px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('合成成功!', 0, 0);
            }

            ctx.restore();
        });
    };

    // 主绘制函数
    const draw = () => {
        if (!ctx) return;

        ctx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height);

        // 绘制所有木签
        gameState.value.sticks.forEach((stick, index) => {
            drawStick(stick, index);
        });

        // 绘制移动动画
        drawMoveAnimations();

        // 绘制成功动画
        drawSuccessAnimations();
    };

    // 游戏主循环
    const gameLoop = () => {
        if (!gameState.value.isRunning) return;

        draw();
        animationFrame = requestAnimationFrame(gameLoop);
    };

    // 开始计时器
    const startTimer = () => {
        if (timerInterval) clearInterval(timerInterval);

        timerInterval = setInterval(() => {
            if (gameState.value.timeLeft > 0 && gameState.value.isRunning) {
                gameState.value.timeLeft--;

                if (gameState.value.timeLeft <= 0) {
                    endGame();
                }
            }
        }, 1000);
    };

    // 点击检测
    const checkClick = (x, y) => {
        // 检查点击的是哪根木签
        for (let stickIndex = 0; stickIndex < gameState.value.sticks.length; stickIndex++) {
            const stick = gameState.value.sticks[stickIndex];

            // 扩大点击区域，使用食物宽度作为点击区域宽度
            const clickWidth = Math.max(160, 120); // 使用食物的标准宽度160，最小120像素
            const stickLeft = stick.x - clickWidth / 2;
            const stickRight = stick.x + clickWidth / 2;
            const stickTop = stick.y - config.stickHeight / 2;
            const stickBottom = stick.y + config.stickHeight / 2;

            if (x >= stickLeft && x <= stickRight && y >= stickTop && y <= stickBottom) {
                handleStickClick(stickIndex);
                break;
            }
        }
    };

    // 处理木签点击
    const handleStickClick = (stickIndex) => {
        const stick = gameState.value.sticks[stickIndex];

        if (gameState.value.selectedFoods) {
            // 已有选中的食物，尝试移动
            const selectedFoods = gameState.value.selectedFoods;

            if (selectedFoods.stickIndex === stickIndex) {
                // 点击同一根木签，取消选中
                gameState.value.selectAnimation = {
                    startTime: Date.now(),
                    isSelecting: false
                };
                setTimeout(() => {
                    gameState.value.selectedFoods = null;
                    gameState.value.selectAnimation = null;
                }, config.selectAnimationDuration);
            } else {
                // 点击不同木签，尝试移动
                if (canMoveTo(stickIndex, selectedFoods.count)) {
                    // 添加移动动画，动画完成后会自动移动食物
                    addMoveAnimation(selectedFoods.stickIndex, stickIndex, selectedFoods.count);
                    gameState.value.selectedFoods = null;
                    gameState.value.selectAnimation = null;
                } else {
                    // 无法移动，取消选中
                    gameState.value.selectAnimation = {
                        startTime: Date.now(),
                        isSelecting: false
                    };
                    setTimeout(() => {
                        gameState.value.selectedFoods = null;
                        gameState.value.selectAnimation = null;
                    }, config.selectAnimationDuration);
                }
            }
        } else {
            // 没有选中的食物，尝试选中当前木签最上面的食物
            if (stick.foods.length > 0) {
                const topFoods = getTopSameFoods(stickIndex);
                if (topFoods.count > 0) {
                    gameState.value.selectedFoods = {
                        stickIndex: stickIndex,
                        count: topFoods.count,
                        type: topFoods.type,
                        isSynthetic: topFoods.isSynthetic
                    };
                    // 启动选中动画
                    gameState.value.selectAnimation = {
                        startTime: Date.now(),
                        isSelecting: true
                    };
                }
            }
        }
    };

    // 事件处理
    const handleClick = (e) => {
        if (!gameState.value.isRunning) return;

        if (e.type === 'touchstart') {
            e.preventDefault();
        }

        const point = e.touches ? e.touches[0] : e;
        const rect = canvasRef.value.getBoundingClientRect();
        const scaleX = canvasRef.value.width / rect.width;
        const scaleY = canvasRef.value.height / rect.height;
        const x = (point.clientX - rect.left) * scaleX;
        const y = (point.clientY - rect.top) * scaleY;

        checkClick(x, y);
    };

    // 添加事件监听
    const addEvents = () => {
        const canvas = canvasRef.value;
        canvas.addEventListener('mousedown', handleClick);
        canvas.addEventListener('touchstart', handleClick, { passive: false });

        // 阻止触摸时的页面滚动
        canvas.addEventListener('touchmove', (e) => {
            e.preventDefault();
        }, { passive: false });
    };

    // 移除事件监听
    const removeEvents = () => {
        const canvas = canvasRef.value;
        canvas.removeEventListener('mousedown', handleClick);
        canvas.removeEventListener('touchstart', handleClick);

        if (animationFrame) {
            cancelAnimationFrame(animationFrame);
        }

        if (timerInterval) {
            clearInterval(timerInterval);
        }

        successSound.stop();
    };

    // 检查游戏是否胜利
    const checkWin = () => {
        // 检查是否所有食物都已合成
        let totalSyntheticFoods = 0;
        gameState.value.sticks.forEach(stick => {
            stick.foods.forEach(food => {
                if (food.isSynthetic) {
                    totalSyntheticFoods++;
                }
            });
        });

        // 根据当前关卡配置检查胜利条件
        const currentConfig = levelConfigs[gameState.value.currentLevel] || levelConfigs[1];
        const isWin = totalSyntheticFoods >= currentConfig.targetSyntheticFoods;

        // 调试信息
        if (isWin) {
            console.log(`关卡${gameState.value.currentLevel}完成！合成食物数量: ${totalSyntheticFoods}/${currentConfig.targetSyntheticFoods}`);
        }

        return isWin;
    };

    // 初始化游戏
    const initGame = async () => {
        if (!canvasRef.value) return;
        
        try {
            ctx = canvasRef.value.getContext('2d');
            initSticks();
            addEvents();
            draw();
            console.log('游戏初始化完成');
        } catch (error) {
            console.error('游戏初始化失败:', error);
        }
    };

    // 开始游戏
    const startGame = () => {
        // 确保所有图片都已加载
        const allImagesLoaded = foodList.every(food =>
            food.img && food.img.complete &&
            food.syntheticImg && food.syntheticImg.complete
        ) && images.stick && images.stick.complete &&
        images.light && images.light.complete;

        if (!allImagesLoaded) {
            console.error('图片资源未完全加载，无法开始游戏');
            return;
        }

        gameState.value = {
            timeLeft: config.gameTime,
            isRunning: true,
            isGameOver: false,
            currentLevel: gameState.value.currentLevel, // 保持当前关卡
            selectedFoods: null,
            selectAnimation: null,
            moveAnimations: [],
            sticks: gameState.value.sticks, // 保持已初始化的木签状态
            animations: [],
            successAnimations: []
        };

        startTimer();
        gameLoop();
    };

    // 结束游戏
    const endGame = () => {
        gameState.value.isRunning = false;
        gameState.value.isGameOver = true;

        if (timerInterval) {
            clearInterval(timerInterval);
        }

        // 判断游戏胜利条件：
        // 1. 当前关卡完成 AND
        // 2. 是第三关（最后一关）OR 时间用完但当前关卡完成
        const currentLevelComplete = checkWin();
        const isGameWin = currentLevelComplete && gameState.value.currentLevel === 3;

        console.log(`游戏结束 - 当前关卡: ${gameState.value.currentLevel}, 关卡完成: ${currentLevelComplete}, 游戏胜利: ${isGameWin}`);

        if (onGameEnd) {
            onGameEnd(isGameWin);
        }
    };

    // 初始化指定关卡
    const initLevel = (level) => {
        gameState.value.currentLevel = level;
        initSticks(level);
        console.log(`初始化第${level}关`);
    };

    return {
        gameState,
        loadAssets,
        initGame,
        startGame,
        endGame,
        addEvents,
        removeEvents,
        checkWin,
        initLevel,
        levelConfigs
    };
}
