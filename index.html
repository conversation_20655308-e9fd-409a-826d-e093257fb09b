<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0,minimal-ui:ios" name="viewport">
    <title>{$zt_name}</title>
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/style.min.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/animate.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/<EMAIL>">
    <link rel="stylesheet" href="css/index.css?v={php} echo mt_rand(1000,999999);{/php}">
    <script defer src="css/polyfill.js?v={php} echo mt_rand(1000,999999);{/php}"></script>
    <style>
        [v-cloak] {
            display: none;
        }
    </style>
</head>

<body>
    <div id="app" class="warp" v-cloak>
        <div class="musicbtn" :class="{ on:on }"  @click="bgClick"></div>
        <div class="page fc" :class="{blur:show}" v-if="page===1">
            <div class="rotation animate__animated animate__fadeIn">
                <van-swipe ref="swipe" class="my-swipe" :autoplay="2000" :show-indicators="false" indicator-color="white" vertical>
                    <van-swipe-item v-for="(item,index) in handleRotation">
                        <p>{{item[0]}}<span>{{item[1]}}</span>{{item[2]}}</p>
                    </van-swipe-item>
                </van-swipe>
            </div>
            <img class="title animate__animated animate__zoomIn" src="img/title.png">
            <div class="start pulsate-bck2" @click="start"></div>
            <div class="button_container">
                <img src="img/button2.png" class="button animate__animated animate__fadeInLeft" @click="page=2">
                <img src="img/button3.png" class="button animate__animated animate__fadeInRight" @click="page=3">
            </div>
        </div>
        <!-- 活动规则页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===2">
            <img src="img/title.png" class="title2 animate__animated animate__zoomIn">
            <div class="area area1">
                <div class="rule" v-html="startData.rule"></div>
                <img src="img/button4.png" class="back animate__animated animate__fadeInUp" @click="page=1">
            </div>
        </div>
        <!-- 我的奖品页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===3">
            <img src="img/title.png" class="title2 animate__animated animate__zoomIn">
            <div class="area area2">
                <div class="prize">
                    <div class="info">
                        <img src="img/jptit1.png" class="jptit mt5">
                        <p class="p2 p3 mt5" v-if="startData.prize">
                            {{startData.prizeTime}}：{{startData.ad}}</p>
                        <div class="p2" v-if="startData.prize">{{startData.prize}}</div>
                        <p class="p2 p1 mt5" v-else>暂未中奖</p>
                    </div>
                    <div class="info">
                        <img src="img/jptit2.png" class="jptit">
                        <p class="p2 mt5">{{startData.userInfo.name}}&nbsp;&nbsp;{{startData.userInfo.phone}}</p>
                        <p class="p2">{{startData.userInfo.area.split(',').join('')}}</p>
                        <p class="p2">{{startData.userInfo.address}}</p>
                    </div>
                    <img src="img/edit.png" class="edit" @click="edit">
                </div>
                <img src="img/button4.png" class="back animate__animated animate__fadeInUp" @click="page=1">
            </div>
        </div>
        <!-- 登记信息页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===4">
            <div class="area area3">
                <form class="form">
                    <div class="form-item">
                        <label>姓　　名:</label>
                        <input type="text" v-model="form.name">
                    </div>
                    <div class="form-item">
                        <label>联系方式:</label>
                        <input type="number" v-model="form.phone">
                    </div>
                    <div class="form-item fs">
                        <label>邮寄地址:</label>
                        <div class="right" @click="focus">
                            <input type="text" placeholder="选择省" v-model="form.area.split(',')[0]" readonly>
                            <input type="text" placeholder="选择市" v-model="form.area.split(',')[1]" readonly>
                            <input type="text" placeholder="选择区" v-model="form.area.split(',')[2]" readonly>
                        </div>
                    </div>
                    <div class="form-item">
                        <label>街道地址:</label>
                        <input type="text" v-model="form.address" @keyup.enter="submit">
                    </div>
                    <div class="form-footer">
                        <p class="fz1">◎</p>
                        <p>免责声明:<br>本活动专题收集的所有信息仅供发放活动奖品使用，在未经得本人同意情况下绝对不会将您的任何资料以任何方式泄露给第三方。由于您自身原因如共享登录账号等导致的个人信息披露，活动方概不负责。
                        </p>
                    </div>
                    <img src="img/button6.png" class="back2 animate__animated animate__fadeInUp" @click="submit">
                </form>
                <van-popup v-model:show="popupShow" round position="bottom">
                    <van-picker show-toolbar title="请选择地区" :columns="options" default-index="11"
                        v-model="selectedValues" @cancel="popupShow = false" @confirm="onConfirm"></van-picker>
                </van-popup>
            </div>
        </div>
        <!-- 游戏页 -->
        <div class="page bj2 fc" :class="{blur:show}" v-if="page===5">
            <div class="game_area">
                <div class="time">{{gameState?.timeLeft}}s</div>
                <div class="level">第{{gameState?.currentLevel}}关</div>
                <canvas ref="gameCanvas" width="750" height="1334"></canvas>
            </div>
            <div class="mask fc" v-if="ct">
                <img src="img/3.png" class="countdown elem3" v-if="ct===3">
                <img src="img/2.png" class="countdown elem3" v-if="ct===2">
                <img src="img/1.png" class="countdown elem3" v-if="ct===1">
            </div>
        </div>
        <!-- 提交成功弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===1">
                <div class="popup popup1">
                    <img src="img/button4_2.png" class="back" @click="reload">
                </div>
            </div>
        </transition>
        <!-- 无机会弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===2">
                <div class="popup popup2">
                    <img src="img/close.png" class="close2" @click="reload">
                </div>
            </div>
        </transition>
        <!-- 游戏成功弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===3">
                <div class="popup popup3">
                    <img src="img/button8.png" class="back" @click="getPrize">
                </div>
            </div>
        </transition>
        <!-- 游戏失败弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===4">
                <div class="popup popup4">
                    <img src="img/button9.png" class="back" @click="reload">
                </div>
            </div>
        </transition>
        <!-- 中奖弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===5">
                <div class="popup popup5">
                    <div class="p3" v-html="'>'+prizeData.ad+'<'"></div>
                    <div class="p4">{{prizeData.prize}}</div>
                    <img src="img/button10.png" class="back" @click="goForm">
                </div>
            </div>
        </transition>
        <!-- 未中奖弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===6">
                <div class="popup popup6">
                    <img src="img/button4.png" class="back" @click="reload">
                </div>
            </div>
        </transition>
    </div>
    
    <!-- <script src="https://ztimg.hefei.cc/static/common/js/libs/vue.js"></script> -->
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/jquery-3.6.0.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/howler.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/preloadjs.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/dayjs.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/html2canvas.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook1.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook2.js"></script>
    <script>
        const { createApp, ref, watch, nextTick } = Vue
    </script>
    <script src="game.js?v={php} echo mt_rand(1000,999999);{/php}"></script>
    <script>
        let rotation = {$rotation|raw};
        window.startData = {
            rotation:rotation,
            jihui: '{$jihui}',
            prize: '{$prize}',
            ad: '{$ad}',
            prizeTime: '{$prizeTime}',
            userInfo: {
                name: '{$name}',
                phone: '{$phone}',
                area: '{$area}',
                address: '{$address}',
            },
            nickname: '{$nickname}',
            avatar: '{$headimgurl}',
            wlq:'{$wlq}',
            endtime: '{$endtime}',
            writeFlag: '{$writeFlag}',
            rule: `{$zt_rule|raw}`,
        }

        const app = createApp({
            setup() {
                const { on, bgClick } = useBgMusic('https://ztimg.hefei.cc/zt2025/dfashmwcyc/123.mp3')//调用景音乐
                setMockPage && setMockPage()//添加案例提示语
                const page = ref(1) //控制页面
                const show = ref(0) //控制弹窗
                const { userInfo, endtime, writeFlag } = startData
                const opportunity = ref((+startData.jihui)) //控制机会
                const handleRotation = startData.rotation.map(item => item.split(',')) // 设置顶部获奖数据
                const start = () => {
                    if (endtime === '1') return vantAlert('活动未开始')
                    if (endtime === '2') return vantAlert('活动已结束')
                    if (opportunity.value >= 1) {
                        page.value = 5
                        opportunity.value--
                        defaultHttp('gamestart', { status: 1 })
                    } else {
                        show.value = 2
                    }
                }

                const edit = () => {
                    if (writeFlag === 0) {
                        return vantAlert('活动已结束');
                    } else {
                        goForm()
                    }
                }
                const goForm = () => {
                    page.value = 4
                    show.value = 0
                }

                // 登记信息功能
                const { form, popupShow, options, focus, onConfirm, check, selectedValues } = createdForm()
                form.value = userInfo
                const submit = throttle(async () => {
                    if (!check()) return
                    const res = await defaultHttp('action', Object.assign({ act: 'sub' }, form.value), { status: 1 })
                    if (res.status == 1) {
                        show.value = 1
                    } else {
                        vantAlert(res.msg)
                    }
                })
                // 刷新页面功能
                const { reload, savePage } = useReload()
                if (savePage) { page.value = +savePage }
                // 判断是否是初次进入网页,执行预加载
                const { loadStart, progress, progressShow, startFlag } = cheackStartPopup([])
                loadStart()
                if(startFlag){
                    page.value = 2
                }
                // 检查未领取
                if (startData.wlq === '1') {
                    vant.showConfirmDialog({
                        title: '温馨提示',
                        message: '您已中奖，请尽快完善领奖信息！',
                        confirmButtonText: '去领取',
                        cancelButtonText: '不领取'
                    }).then(() => {
                        show.value = 0
                        page.value = 4
                    })
                }

                const gameEnd = throttle(async (e) => {
                    const res = await defaultHttp('gameEnd', {
                        cg: e ? 1 : 0,
                    }, { status: 1 })
                    if (res.status === 1) {
                        show.value = 3
                    } else if(res.status === 2) {
                        show.value = 4
                    } else {
                        vantAlert(res.msg, reload)
                    }
                })

                // 抽奖逻辑
                const prizeData = ref({ prize: startData.prize, ad: startData.ad, prizeType: 0 })
                const getPrize = throttle(async () => {
                    const res = await defaultHttp('getprize', {}, { status: 1, msg: '抽中奖品', data: { prize: '扑克牌一份', ad: '云水雅玩礼', prizeType: 1 } })
                    if (res.status === 1) {
                        show.value = 5
                        prizeData.value = res.data
                    } else if (res.status === 2) {
                        show.value = 6 //未中奖
                    } else {
                        vantAlert(res.msg, reload)
                    }
                })

                // 游戏逻辑
                const gameCanvas = ref(null)
                const {
                    gameState,
                    startGame,
                    initGame,
                    loadAssets,
                    addEvents,
                    removeEvents,
                } = useKaoGame(gameCanvas, gameEnd)
                loadAssets()
                const ct = ref(3)
                // 监听页面变化，当进入游戏页面时初始化游戏
                watch(page, async (newPage) => {
                    if (newPage === 5) {
                        setTimeout(async() => {
                            ct.value = 3
                            await initGame()
                            let timer = setInterval(async() => {
                                if (ct.value > 0) {
                                    ct.value--
                                }else{
                                    clearInterval(timer)
                                    startGame()
                                }
                            }, 1000);
                        }, 0);
                    }
                })

                return {
                    startData, page, show,
                    handleRotation, edit, goForm,
                    on, bgClick,
                    start, submit, reload,
                    form, popupShow, options, focus, onConfirm, check, selectedValues,
                    prizeData, getPrize,
                    gameCanvas, gameState,ct
                }
            },
        });
        app.use(vant);
        app.mount('#app');
    </script>
        <!--分享-->
{include file="share"/}
</body>

</html>



