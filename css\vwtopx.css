@font-face {
  font-family: '思源宋体';
  src: url(https://ztimg.hefei.cc/static/common/fonts/思源宋体.otf);
}
body,
div,
h1,
h2,
h3,
h4,
h5,
h6,
html,
li,
p,
span {
  font-size: 2vh;
}
.musicbtn {
  width: 6vh;
  height: 6vh;
  top: 2vh;
  right: 2vh;
  background: url(../img/music.png) no-repeat center center / contain;
  z-index: 11;
}
.warp {
  margin: 0 auto;
  min-height: 102vh;
  height: 100vh;
  width: 60vh;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: '思源宋体';
  background: linear-gradient(180deg, #2a3d87 0%, #ee7c47 100%);
}
.warp .swipe_container {
  width: 100%;
  height: 100%;
}
.warp .page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  color: #352219;
  background: url(../img/bj.jpg) no-repeat center center / 60vh auto;
}
.warp .page .rotation {
  margin-top: 4vh;
  width: 37vh;
  height: 4vh;
  border: 1px solid #fff;
  border-radius: 2vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 3;
  color: #fff;
}
.warp .page .rotation .van-swipe {
  height: 5vh;
}
.warp .page .rotation .van-swipe .van-swipe-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.warp .page .rotation p {
  text-align: center;
  white-space: nowrap;
  font-size: 2vh;
}
.warp .page .rotation p span {
  font-size: 2vh;
}
.warp .page .title {
  margin-top: 1vh;
  width: 47vh;
  z-index: 2;
}
.warp .page .title2 {
  margin-top: 2vh;
  width: 47vh;
  z-index: 3;
}
.warp .page .start {
  margin-top: 43vh;
  width: 25vh;
  height: 12vh;
  flex-shrink: 0;
  background: url(../img/start.png) no-repeat center center / 100% 100%;
  z-index: 2;
}
.warp .page .bg2 {
  width: 60vh;
  position: absolute;
  left: 0;
  bottom: 0;
  pointer-events: none;
}
.warp .page .bg3 {
  width: 60vh;
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
}
.warp .page .button_container {
  position: relative;
  z-index: 2;
  margin-top: 3vh;
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 0 4vh;
}
.warp .page .button_container .button {
  width: 21vh;
}
.warp .page .game_area {
  width: 60vh;
  height: 107vh;
  display: flex;
  justify-content: center;
  align-items: center;
}
.warp .page .game_area .time {
  width: 14vh;
  height: 6vh;
  background: url(../img/time.png) no-repeat center center / 100% 100%;
  color: #fff;
  position: absolute;
  top: 6vh;
  left: 3vh;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 3vh;
  padding-left: 4vh;
}
.warp .page .game_area .level {
  width: 19vh;
  height: 8vh;
  background: url(../img/level.png) no-repeat center center / 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 3vh;
  position: absolute;
  top: 6vh;
}
.warp .page .game_area canvas {
  width: 100%;
  height: 100%;
}
.warp .bj2 {
  background-image: url(../img/bj2.jpg);
}
.warp .bj3 {
  background-image: url(../img/bj3.jpg);
}
.blur {
  filter: blur(1vh);
}
.fc {
  justify-content: center;
}
.area {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  background: url(../img/area.png) no-repeat center center / 100% 100%;
}
.area .back {
  position: absolute;
  bottom: -2vh;
  width: 18vh;
}
.area .back2 {
  position: absolute;
  bottom: -4vh;
  width: 22vh;
}
.area .submit {
  position: absolute;
  bottom: -9vh;
  width: 24vh;
}
.area .rule {
  width: 100%;
  padding: 0 2vh;
  margin: 7vh 0 6vh;
  flex: 1;
  overflow-y: auto;
  line-height: 1.5;
  text-align: justify;
  letter-spacing: -0vh;
  position: relative;
}
.area .prize {
  margin-top: 7vh;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.area .prize .mt5 {
  margin-top: 1vh;
}
.area .prize .info {
  padding: 2vh 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 10vh;
  width: 42vh;
}
.area .prize .info:first-child {
  border-bottom: 1px dashed #215444;
}
.area .prize .info .p2 {
  font-size: 3vh;
  line-height: 4vh;
  max-width: 45vh;
  text-align: center;
}
.area .prize .info .jptit {
  width: 18vh;
  margin-bottom: 1vh;
}
.area .prize .edit {
  width: 24vh;
}
.area .form {
  width: 100%;
  padding: 14vh 3vh 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.area .form .form-item {
  margin-left: 0;
  margin-bottom: 3vh;
  display: flex;
  align-items: center;
}
.area .form .form-item label {
  width: 16vh;
  font-weight: bold;
  font-size: 3vh;
  white-space: nowrap;
  color: #000;
  flex-shrink: 0;
}
.area .form .form-item div input {
  margin-bottom: 2vh;
}
.area .form .form-item div input:nth-last-child(1) {
  margin-bottom: 0;
}
.area .form .form-item .right {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.area .form .form-item input {
  margin-left: 0vh;
  padding-left: 2vh;
  width: 30vh;
  height: 5vh;
  border: 1px #000 solid;
  flex-shrink: 0;
  opacity: 1;
  color: #000;
  font-size: 3vh;
}
.area .form .form-item input::-webkit-input-placeholder {
  color: #000;
  opacity: 0.6;
}
.area .form .form-item input:-moz-placeholder {
  color: #000;
  opacity: 0.6;
}
.area .form .form-item input::-moz-placeholder {
  color: #000;
  opacity: 0.6;
}
.area .form .form-item input:-ms-input-placeholder {
  color: #000;
  opacity: 0.6;
}
.area .form .form-item #getArea {
  opacity: 0;
  position: absolute;
  z-index: -1;
}
.area .form .form-footer {
  margin-top: -6vh;
  display: flex;
  width: 200%;
  transform: scale(0.5);
  color: #000;
}
.area .form .form-footer .fz1 {
  font-size: 4vh;
}
.area .form .form-footer p {
  font-size: 4vh;
  line-height: 1.5;
  text-align: justify;
  letter-spacing: 0vh;
}
.area .form .button {
  margin-top: -3vh;
  width: 18vh;
}
.area .form .fs {
  align-items: flex-start;
}
.area .form .fs label {
  margin-top: 0vh;
}
.area1 {
  margin-top: 1vh;
  width: 54vh;
  height: 69vh;
  background: url(../img/area1.png) no-repeat center center / 100% 100%;
}
.area2 {
  margin-top: 1vh;
  width: 53vh;
  height: 67vh;
  background: url(../img/area2.png) no-repeat center center / 100% 100%;
}
.area3 {
  margin-top: 1vh;
  width: 55vh;
  height: 85vh;
  background: url(../img/area3.png) no-repeat center center / 100% 100%;
}
.mask {
  z-index: 10;
  position: fixed;
  top: 0;
  left: 0;
  min-height: 100vh;
  width: 60vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(18, 45, 29, 0.3);
  transform: translateX(-50%);
  left: 50%;
  color: #352219;
  font-weight: 300;
}
.mask .countdown {
  height: 36vh;
}
.mask .popup {
  margin-top: -1vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.mask .popup .back {
  width: 23vh;
  position: absolute;
  bottom: -4vh;
}
.mask .popup1 {
  width: 46vh;
  height: 33vh;
  background: url(../img/popup1.png) no-repeat center top / 100% 100%;
}
.mask .popup2 {
  width: 47vh;
  height: 33vh;
  background: url(../img/popup2.png) no-repeat center top / 100% 100%;
}
.mask .popup2 .close2 {
  width: 5vh;
  position: absolute;
  top: 1vh;
  right: -2vh;
}
.mask .popup3 {
  width: 55vh;
  height: 38vh;
  background: url(../img/popup3.png) no-repeat center top / 100% 100%;
}
.mask .popup4 {
  width: 55vh;
  height: 38vh;
  background: url(../img/popup4.png) no-repeat center top / 100% 100%;
}
.mask .popup5 {
  width: 55vh;
  height: 38vh;
  background: url(../img/popup5.png) no-repeat center top / 100% 100%;
  padding-top: 0vh;
}
.mask .popup5 .p3 {
  margin-top: -2vh;
  font-size: 4vh;
  white-space: nowrap;
  font-weight: bold;
}
.mask .popup5 .p4 {
  font-size: 4vh;
  white-space: nowrap;
}
.mask .popup6 {
  width: 55vh;
  height: 38vh;
  background: url(../img/popup6.png) no-repeat center top / 100% 100%;
}
@keyframes elem3 {
  from {
    transform: scale(4);
    opacity: 0;
  }
  to {
    transform: scale(1.2);
    opacity: 1;
  }
}
.elem3 {
  animation: elem3 0.6s ease both;
}
